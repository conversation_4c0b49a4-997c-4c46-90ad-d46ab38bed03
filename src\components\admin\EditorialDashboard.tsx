'use client';

import React, { useState, useEffect } from 'react';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  User, 
  Calendar,
  Filter,
  Search,
  Eye,
  Edit3,
  MoreHorizontal
} from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface SubmissionItem {
  id: string;
  name: string;
  url: string;
  description: string;
  category: string;
  submitterName: string;
  submitterEmail: string;
  status: 'pending' | 'under_review' | 'approved' | 'rejected' | 'published';
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  reviewNotes?: string;
  priority: 'high' | 'normal' | 'low';
}

interface EditorialStats {
  totalSubmissions: number;
  pendingReview: number;
  underReview: number;
  approvedToday: number;
  rejectedToday: number;
  averageReviewTime: number;
}

interface EditorialDashboardProps {
  className?: string;
}

export function EditorialDashboard({ className = '' }: EditorialDashboardProps) {
  const [submissions, setSubmissions] = useState<SubmissionItem[]>([]);
  const [stats, setStats] = useState<EditorialStats>({
    totalSubmissions: 0,
    pendingReview: 0,
    underReview: 0,
    approvedToday: 0,
    rejectedToday: 0,
    averageReviewTime: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadEditorialData();
  }, []);

  const loadEditorialData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load submissions and stats
      const [submissionsResponse, statsResponse] = await Promise.all([
        fetch('/api/admin/editorial/submissions', {
          headers: {
            'x-admin-api-key': 'admin-dashboard-access'
          }
        }),
        fetch('/api/admin/editorial/stats', {
          headers: {
            'x-admin-api-key': 'admin-dashboard-access'
          }
        })
      ]);

      if (!submissionsResponse.ok || !statsResponse.ok) {
        throw new Error('Failed to load editorial data');
      }

      const submissionsData = await submissionsResponse.json();
      const statsData = await statsResponse.json();

      setSubmissions(submissionsData.submissions || []);
      setStats(statsData.stats || stats);
    } catch (err) {
      console.error('Failed to load editorial data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load data');
      
      // Mock data for development
      setSubmissions([
        {
          id: '1',
          name: 'AI Writing Assistant',
          url: 'https://example.com',
          description: 'An advanced AI tool for content creation and writing assistance.',
          category: 'writing-tools',
          submitterName: 'John Doe',
          submitterEmail: '<EMAIL>',
          status: 'pending',
          submittedAt: '2024-01-15T10:30:00Z',
          priority: 'normal'
        },
        {
          id: '2',
          name: 'Image Generator Pro',
          url: 'https://imagegen.com',
          description: 'Professional AI image generation with advanced customization options.',
          category: 'image-generators',
          submitterName: 'Jane Smith',
          submitterEmail: '<EMAIL>',
          status: 'under_review',
          submittedAt: '2024-01-14T15:45:00Z',
          reviewedBy: 'admin',
          priority: 'high'
        }
      ]);
      
      setStats({
        totalSubmissions: 156,
        pendingReview: 23,
        underReview: 8,
        approvedToday: 12,
        rejectedToday: 3,
        averageReviewTime: 2.5
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-400 bg-yellow-400/10';
      case 'under_review': return 'text-blue-400 bg-blue-400/10';
      case 'approved': return 'text-green-400 bg-green-400/10';
      case 'rejected': return 'text-red-400 bg-red-400/10';
      case 'published': return 'text-purple-400 bg-purple-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock size={16} />;
      case 'under_review': return <AlertCircle size={16} />;
      case 'approved': return <CheckCircle size={16} />;
      case 'rejected': return <XCircle size={16} />;
      case 'published': return <CheckCircle size={16} />;
      default: return <Clock size={16} />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-400';
      case 'normal': return 'text-gray-400';
      case 'low': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const filteredSubmissions = submissions.filter(submission => {
    const matchesStatus = selectedStatus === 'all' || submission.status === selectedStatus;
    const matchesSearch = searchTerm === '' || 
      submission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      submission.submitterName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      submission.category.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesStatus && matchesSearch;
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className={`min-h-screen bg-zinc-900 text-white font-roboto ${className}`}>
        <div className="container mx-auto px-4 py-8" style={{ maxWidth: 'var(--container-width)' }}>
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
              <p className="text-gray-300">Loading editorial dashboard...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-zinc-900 text-white font-roboto ${className}`}>
      <div className="container mx-auto px-4 py-8" style={{ maxWidth: 'var(--container-width)' }}>
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Editorial Dashboard</h1>
            <p className="text-gray-300">Manage user submissions and editorial workflow</p>
          </div>
          
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={loadEditorialData}
            >
              Refresh
            </Button>
            <Button
              variant="primary"
              size="sm"
              onClick={() => window.location.href = '/admin'}
            >
              ← Back to Admin
            </Button>
          </div>
        </div>

        {error && (
          <div className="bg-red-900/20 border border-red-500 rounded-lg p-4 mb-6">
            <p className="text-red-400">{error}</p>
          </div>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-8">
          <Card className="bg-zinc-800 border border-zinc-700 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Total Submissions</p>
                <p className="text-2xl font-bold text-white">{stats.totalSubmissions}</p>
              </div>
              <div className="text-blue-400">
                <User size={24} />
              </div>
            </div>
          </Card>

          <Card className="bg-zinc-800 border border-zinc-700 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Pending Review</p>
                <p className="text-2xl font-bold text-yellow-400">{stats.pendingReview}</p>
              </div>
              <div className="text-yellow-400">
                <Clock size={24} />
              </div>
            </div>
          </Card>

          <Card className="bg-zinc-800 border border-zinc-700 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Under Review</p>
                <p className="text-2xl font-bold text-blue-400">{stats.underReview}</p>
              </div>
              <div className="text-blue-400">
                <AlertCircle size={24} />
              </div>
            </div>
          </Card>

          <Card className="bg-zinc-800 border border-zinc-700 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Approved Today</p>
                <p className="text-2xl font-bold text-green-400">{stats.approvedToday}</p>
              </div>
              <div className="text-green-400">
                <CheckCircle size={24} />
              </div>
            </div>
          </Card>

          <Card className="bg-zinc-800 border border-zinc-700 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Rejected Today</p>
                <p className="text-2xl font-bold text-red-400">{stats.rejectedToday}</p>
              </div>
              <div className="text-red-400">
                <XCircle size={24} />
              </div>
            </div>
          </Card>

          <Card className="bg-zinc-800 border border-zinc-700 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Avg Review Time</p>
                <p className="text-2xl font-bold text-white">{stats.averageReviewTime}h</p>
              </div>
              <div className="text-purple-400">
                <Calendar size={24} />
              </div>
            </div>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="bg-zinc-800 border border-zinc-700 p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search submissions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Filter size={20} className="text-gray-400" />
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="under_review">Under Review</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                  <option value="published">Published</option>
                </select>
              </div>
            </div>
          </div>
        </Card>

        {/* Submissions Table */}
        <Card className="bg-zinc-800 border border-zinc-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-white">Submissions ({filteredSubmissions.length})</h2>
          </div>

          {filteredSubmissions.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 text-lg mb-2">No submissions found</div>
              <p className="text-gray-500">Try adjusting your filters or search terms</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-zinc-700">
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Tool</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Submitter</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Category</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Status</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Priority</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Submitted</th>
                    <th className="text-left py-3 px-4 text-gray-300 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredSubmissions.map((submission) => (
                    <tr key={submission.id} className="border-b border-zinc-700/50 hover:bg-zinc-700/30 transition-colors">
                      <td className="py-4 px-4">
                        <div>
                          <div className="font-medium text-white">{submission.name}</div>
                          <div className="text-sm text-gray-400 truncate max-w-xs">{submission.description}</div>
                          <a
                            href={submission.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-xs text-orange-400 hover:text-orange-300 transition-colors"
                          >
                            {submission.url}
                          </a>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div>
                          <div className="text-white">{submission.submitterName}</div>
                          <div className="text-sm text-gray-400">{submission.submitterEmail}</div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <span className="px-2 py-1 bg-zinc-700 text-gray-300 rounded-md text-sm">
                          {submission.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                      </td>
                      <td className="py-4 px-4">
                        <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm ${getStatusColor(submission.status)}`}>
                          {getStatusIcon(submission.status)}
                          {submission.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <span className={`font-medium ${getPriorityColor(submission.priority)}`}>
                          {submission.priority.charAt(0).toUpperCase() + submission.priority.slice(1)}
                        </span>
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-sm text-gray-300">{formatDate(submission.submittedAt)}</div>
                        {submission.reviewedAt && (
                          <div className="text-xs text-gray-500">
                            Reviewed: {formatDate(submission.reviewedAt)}
                          </div>
                        )}
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {/* Handle view */}}
                            className="p-2"
                          >
                            <Eye size={16} />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {/* Handle edit */}}
                            className="p-2"
                          >
                            <Edit3 size={16} />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {/* Handle more actions */}}
                            className="p-2"
                          >
                            <MoreHorizontal size={16} />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
}
