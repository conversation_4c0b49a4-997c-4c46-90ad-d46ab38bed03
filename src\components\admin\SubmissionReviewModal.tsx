'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  X, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  ExternalLink,
  User,
  Mail,
  Calendar,
  Tag,
  Globe
} from 'lucide-react';
import { Button } from '@/components/ui/Button';

// Validation schema for review form
const reviewSchema = z.object({
  decision: z.enum(['approve', 'reject', 'needs_revision'], {
    required_error: 'Please select a decision'
  }),
  reviewNotes: z.string().min(10, 'Review notes must be at least 10 characters').max(1000, 'Review notes must be less than 1000 characters'),
  priority: z.enum(['high', 'normal', 'low']).optional(),
  featuredDate: z.string().optional(),
  editorialText: z.string().optional(),
  qualityScore: z.number().min(1).max(10).optional()
});

type ReviewFormData = z.infer<typeof reviewSchema>;

interface SubmissionItem {
  id: string;
  name: string;
  url: string;
  description: string;
  category: string;
  subcategory?: string;
  submitterName: string;
  submitterEmail: string;
  status: 'pending' | 'under_review' | 'approved' | 'rejected' | 'published';
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  reviewNotes?: string;
  priority: 'high' | 'normal' | 'low';
  pricingType?: string;
}

interface SubmissionReviewModalProps {
  submission: SubmissionItem;
  isOpen: boolean;
  onClose: () => void;
  onSubmitReview: (submissionId: string, reviewData: ReviewFormData) => Promise<void>;
}

export function SubmissionReviewModal({ 
  submission, 
  isOpen, 
  onClose, 
  onSubmitReview 
}: SubmissionReviewModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm<ReviewFormData>({
    resolver: zodResolver(reviewSchema),
    defaultValues: {
      decision: undefined,
      reviewNotes: '',
      priority: submission.priority,
      qualityScore: 7
    }
  });

  const selectedDecision = watch('decision');

  const onSubmit = async (data: ReviewFormData) => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);
      
      await onSubmitReview(submission.id, data);
      
      reset();
      onClose();
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : 'Failed to submit review');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDecisionColor = (decision: string) => {
    switch (decision) {
      case 'approve': return 'text-green-400 bg-green-400/10 border-green-400/20';
      case 'reject': return 'text-red-400 bg-red-400/10 border-red-400/20';
      case 'needs_revision': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  const getDecisionIcon = (decision: string) => {
    switch (decision) {
      case 'approve': return <CheckCircle size={20} />;
      case 'reject': return <XCircle size={20} />;
      case 'needs_revision': return <AlertTriangle size={20} />;
      default: return null;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-zinc-800 border border-zinc-700 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-zinc-700">
          <div>
            <h2 className="text-2xl font-bold text-white">Review Submission</h2>
            <p className="text-gray-300 mt-1">Evaluate and make a decision on this tool submission</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors p-2"
          >
            <X size={24} />
          </button>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Submission Details */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Submission Details</h3>
                
                <div className="bg-zinc-900 border border-zinc-700 rounded-lg p-4 space-y-4">
                  <div>
                    <h4 className="text-xl font-bold text-white">{submission.name}</h4>
                    <a 
                      href={submission.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-2 text-orange-400 hover:text-orange-300 transition-colors mt-1"
                    >
                      <Globe size={16} />
                      {submission.url}
                      <ExternalLink size={14} />
                    </a>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-300">Description</label>
                    <p className="text-white mt-1">{submission.description}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-300">Category</label>
                      <div className="flex items-center gap-2 mt-1">
                        <Tag size={16} className="text-gray-400" />
                        <span className="text-white">
                          {submission.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                      </div>
                    </div>

                    {submission.subcategory && (
                      <div>
                        <label className="text-sm font-medium text-gray-300">Subcategory</label>
                        <p className="text-white mt-1">{submission.subcategory}</p>
                      </div>
                    )}
                  </div>

                  {submission.pricingType && (
                    <div>
                      <label className="text-sm font-medium text-gray-300">Pricing Type</label>
                      <p className="text-white mt-1 capitalize">{submission.pricingType}</p>
                    </div>
                  )}

                  <div>
                    <label className="text-sm font-medium text-gray-300">Submitter Information</label>
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center gap-2">
                        <User size={16} className="text-gray-400" />
                        <span className="text-white">{submission.submitterName}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Mail size={16} className="text-gray-400" />
                        <a 
                          href={`mailto:${submission.submitterEmail}`}
                          className="text-orange-400 hover:text-orange-300 transition-colors"
                        >
                          {submission.submitterEmail}
                        </a>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-300">Submission Date</label>
                    <div className="flex items-center gap-2 mt-1">
                      <Calendar size={16} className="text-gray-400" />
                      <span className="text-white">{formatDate(submission.submittedAt)}</span>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-300">Current Status</label>
                    <div className="mt-1">
                      <span className="px-3 py-1 bg-zinc-700 text-gray-300 rounded-md text-sm">
                        {submission.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Review Form */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Editorial Review</h3>
                
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  {submitError && (
                    <div className="bg-red-900/20 border border-red-500 rounded-lg p-4">
                      <p className="text-red-400 text-sm">{submitError}</p>
                    </div>
                  )}

                  {/* Decision */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">
                      Review Decision *
                    </label>
                    <div className="space-y-3">
                      {[
                        { value: 'approve', label: 'Approve', description: 'Accept and publish this submission' },
                        { value: 'needs_revision', label: 'Needs Revision', description: 'Request changes before approval' },
                        { value: 'reject', label: 'Reject', description: 'Decline this submission' }
                      ].map((option) => (
                        <label key={option.value} className="flex items-start gap-3 cursor-pointer">
                          <input
                            type="radio"
                            {...register('decision')}
                            value={option.value}
                            className="mt-1 text-orange-500 focus:ring-orange-500"
                          />
                          <div className="flex-1">
                            <div className={`flex items-center gap-2 ${selectedDecision === option.value ? getDecisionColor(option.value) : 'text-gray-300'}`}>
                              {getDecisionIcon(option.value)}
                              <span className="font-medium">{option.label}</span>
                            </div>
                            <p className="text-sm text-gray-400 mt-1">{option.description}</p>
                          </div>
                        </label>
                      ))}
                    </div>
                    {errors.decision && (
                      <p className="mt-2 text-sm text-red-400">{errors.decision.message}</p>
                    )}
                  </div>

                  {/* Review Notes */}
                  <div>
                    <label htmlFor="reviewNotes" className="block text-sm font-medium text-gray-300 mb-2">
                      Review Notes *
                    </label>
                    <textarea
                      id="reviewNotes"
                      {...register('reviewNotes')}
                      rows={4}
                      className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                        errors.reviewNotes ? 'border-red-500' : 'border-zinc-600'
                      }`}
                      placeholder="Provide detailed feedback about this submission..."
                    />
                    {errors.reviewNotes && (
                      <p className="mt-1 text-sm text-red-400">{errors.reviewNotes.message}</p>
                    )}
                  </div>

                  {/* Priority and Quality Score */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="priority" className="block text-sm font-medium text-gray-300 mb-2">
                        Priority
                      </label>
                      <select
                        id="priority"
                        {...register('priority')}
                        className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      >
                        <option value="low">Low</option>
                        <option value="normal">Normal</option>
                        <option value="high">High</option>
                      </select>
                    </div>

                    <div>
                      <label htmlFor="qualityScore" className="block text-sm font-medium text-gray-300 mb-2">
                        Quality Score (1-10)
                      </label>
                      <input
                        type="number"
                        id="qualityScore"
                        {...register('qualityScore', { valueAsNumber: true })}
                        min="1"
                        max="10"
                        className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  {/* Conditional Fields */}
                  {selectedDecision === 'approve' && (
                    <div className="space-y-4 p-4 bg-green-900/10 border border-green-500/20 rounded-lg">
                      <h4 className="text-green-400 font-medium">Approval Settings</h4>
                      
                      <div>
                        <label htmlFor="featuredDate" className="block text-sm font-medium text-gray-300 mb-2">
                          Featured Date (Optional)
                        </label>
                        <input
                          type="date"
                          id="featuredDate"
                          {...register('featuredDate')}
                          className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                        />
                      </div>

                      <div>
                        <label htmlFor="editorialText" className="block text-sm font-medium text-gray-300 mb-2">
                          Editorial Text (Optional)
                        </label>
                        <textarea
                          id="editorialText"
                          {...register('editorialText')}
                          rows={2}
                          className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          placeholder="Custom editorial text for this tool..."
                        />
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-4 pt-4">
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="flex-1 bg-orange-500 hover:bg-orange-600 disabled:bg-orange-700 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-md transition-colors"
                    >
                      {isSubmitting ? 'Submitting Review...' : 'Submit Review'}
                    </button>

                    <button
                      type="button"
                      onClick={onClose}
                      className="px-6 py-2 bg-zinc-700 hover:bg-zinc-600 text-white font-medium rounded-md transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
